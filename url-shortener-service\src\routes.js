const express = require('express');
const { customAlphabet } = require('nanoid');
const Url = require('./model');
const Click = require('./clickModel');
const { collectAnalyticsData, collectAnalyticsDataAsync } = require('./analytics');
const router = express.Router();

// Create a custom nanoid that only uses alphanumeric characters
const nanoid = customAlphabet('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz', 6);

// Health check endpoint
router.get('/health', (req, res) => {
  res.status(200).json({
    status: 'ok',
    service: 'url-shortener',
    timestamp: new Date().toISOString()
  });
});

router.post('/shorten', async (req, res) => {
  const { url, customCode, allowDuplicates } = req.body;

  if (!url) {
    return res.status(400).json({ error: 'URL is required' });
  }

  try {
    // Validate custom code if provided
    if (customCode) {
      if (!/^[a-zA-Z0-9\-_]+$/.test(customCode)) {
        return res.status(400).json({ error: 'Custom code can only contain letters, numbers, hyphens, and underscores' });
      }

      if (customCode.length < 2 || customCode.length > 50) {
        return res.status(400).json({ error: 'Custom code must be between 2 and 50 characters' });
      }

      // Check if custom code already exists
      const existingCustom = await Url.findOne({ shortCode: customCode });
      if (existingCustom) {
        return res.status(409).json({ error: 'Custom code already exists. Please choose a different one.' });
      }
    }

    // Check for duplicate URLs if not allowed
    if (!allowDuplicates) {
      const existingUrl = await Url.findOne({ originalUrl: url });
      if (existingUrl) {
        const shortUrl = `${process.env.BASE_URL}/${existingUrl.shortCode}`;
        return res.json({
          shortUrl,
          shortCode: existingUrl.shortCode,
          message: 'URL already exists, returning existing short code'
        });
      }
    }

    // Generate code (custom or random)
    const code = customCode || nanoid(6);
    const shortUrl = `${process.env.BASE_URL}/${code}`;

    // Create new URL entry
    const newUrl = await Url.create({
      originalUrl: url,
      shortCode: code,
      isCustom: !!customCode
    });

    console.log(`🔗 Created ${customCode ? 'custom' : 'random'} short URL: ${code} -> ${url}`);

    res.json({
      shortUrl,
      shortCode: code,
      isCustom: !!customCode,
      message: 'Short URL created successfully'
    });
  } catch (err) {
    console.error('Error creating short URL:', err.message);

    // Handle duplicate key error (race condition)
    if (err.code === 11000) {
      return res.status(409).json({ error: 'Short code already exists. Please try again.' });
    }

    res.status(500).json({ error: 'Failed to shorten URL' });
  }
});

// Advanced Analytics Endpoints (MUST be before /:code route)

// Get overall analytics for all URLs
router.get('/analytics', async (req, res) => {
  const { days = 30 } = req.query;

  try {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(days));

    const [clicks, urls] = await Promise.all([
      Click.find({ timestamp: { $gte: startDate } }),
      Url.find()
    ]);

    const analytics = {
      overview: {
        totalUrls: urls.length,
        totalClicks: clicks.length,
        uniqueVisitors: new Set(clicks.map(c => c.ipAddress)).size,
        averageClicksPerUrl: clicks.length / urls.length || 0
      },

      // Top performing URLs
      topUrls: getTopUrls(clicks, urls),

      // Geographic insights
      topCountries: getGeographicDistribution(clicks, 10),

      // Technology insights
      deviceBreakdown: getDistribution(clicks, 'deviceType'),
      browserBreakdown: getDistribution(clicks, 'browser', 10),

      // Traffic sources
      trafficSources: getDistribution(clicks, 'referrerType'),

      // Time-based trends
      dailyTrend: getDailyDistribution(clicks),
      hourlyTrend: getHourlyDistribution(clicks)
    };

    res.json(analytics);
  } catch (err) {
    console.error('Overall analytics error:', err);
    res.status(500).json({ error: 'Failed to fetch overall analytics' });
  }
});

// Get comprehensive analytics for a specific URL
router.get('/analytics/:code', async (req, res) => {
  const { code } = req.params;
  const { days = 30 } = req.query;

  try {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(days));

    const clicks = await Click.find({
      shortCode: code,
      timestamp: { $gte: startDate }
    }).sort({ timestamp: -1 });

    const analytics = {
      totalClicks: clicks.length,
      uniqueClicks: new Set(clicks.map(c => c.ipAddress)).size,
      timeRange: { start: startDate, end: new Date() },

      // Geographic distribution
      countries: getGeographicDistribution(clicks, 10),
      cities: getDistribution(clicks, 'city'),

      // Device analytics
      devices: getDistribution(clicks, 'deviceType'),
      browsers: getDistribution(clicks, 'browser'),
      operatingSystems: getDistribution(clicks, 'os'),

      // Referrer analytics
      referrers: getDistribution(clicks, 'referrerType'),
      referrerDomains: getDistribution(clicks, 'referrerDomain'),

      // Time-based analytics
      hourlyDistribution: getHourlyDistribution(clicks),
      dailyDistribution: getDailyDistribution(clicks),

      // Performance metrics
      averageResponseTime: clicks.reduce((sum, c) => sum + (c.responseTime || 0), 0) / clicks.length || 0,

      // Recent clicks (last 10)
      recentClicks: clicks.slice(0, 10).map(c => ({
        timestamp: c.timestamp,
        country: c.country,
        countryCode: c.countryCode,
        city: c.city,
        region: c.region,
        ipAddress: c.ipAddress,
        org: c.org,
        postal: c.postal,
        device: c.deviceType,
        browser: c.browser,
        referrer: c.referrerType
      }))
    };

    res.json(analytics);
  } catch (err) {
    console.error('Analytics error:', err);
    res.status(500).json({ error: 'Failed to fetch analytics' });
  }
});

// Redirect to original URL (MUST be after specific routes)
router.get('/:code', async (req, res) => {
  const { code } = req.params;
  const startTime = Date.now();

  try {
    const url = await Url.findOne({ shortCode: code });
    if (url) {
      // Collect comprehensive analytics data using ipinfo.io
      const analyticsData = await collectAnalyticsDataAsync(req, code, startTime);

      // Log the click with detailed analytics
      await Click.create(analyticsData);

      // Update URL click count
      await Url.updateOne(
        { shortCode: code },
        { $inc: { clickCount: 1 } }
      );

      console.log(`📊 Click tracked for ${code}: ${analyticsData.city}, ${analyticsData.country} (${analyticsData.countryCode}), ${analyticsData.deviceType}, ${analyticsData.browser}`);

      return res.redirect(url.originalUrl);
    } else {
      // Redirect to homepage if URL not found
      return res.redirect(process.env.BASE_URL || 'https://goqr.info');
    }
  } catch (err) {
    console.error('Error tracking click:', err);
    res.status(500).send('Server error');
  }
});

router.get('/clicks/:code', async (req, res) => {
  const { code } = req.params;
  try {
    const clicks = await Click.find({ shortCode: code });
    res.json({ clicks });
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch click data' });
  }
});

router.get('/stats/:code', async (req, res) => {
  const { code } = req.params;
  try {
    const count = await Click.countDocuments({ shortCode: code });
    res.json({ shortCode: code, totalClicks: count });
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch stats' });
  }
});

router.get('/admin/urls', async (req, res) => {
  try {
    const urls = await Url.find().sort({ createdAt: -1 });

    // Add additional metadata for admin view
    const urlsWithMetadata = urls.map(url => ({
      ...url.toObject(),
      type: url.isCustom ? 'Custom' : 'Random',
      codeLength: url.shortCode.length
    }));

    res.json(urlsWithMetadata);
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch URLs' });
  }
});

router.delete('/admin/delete/:code', async (req, res) => {
  const { code } = req.params;
  try {
    await Url.deleteOne({ shortCode: code });
    await Click.deleteMany({ shortCode: code });
    res.json({ message: 'URL deleted' });
  } catch (err) {
    res.status(500).json({ error: 'Failed to delete URL' });
  }
});



// Helper functions for analytics
function getDistribution(clicks, field, limit = 5) {
  const distribution = {};
  clicks.forEach(click => {
    const value = click[field] || 'Unknown';
    distribution[value] = (distribution[value] || 0) + 1;
  });

  return Object.entries(distribution)
    .sort(([,a], [,b]) => b - a)
    .slice(0, limit)
    .map(([name, count]) => ({ name, count, percentage: (count / clicks.length * 100).toFixed(1) }));
}

// Enhanced geographic distribution with city information
function getGeographicDistribution(clicks, limit = 10) {
  const distribution = {};
  clicks.forEach(click => {
    const country = click.country || 'Unknown';
    const city = click.city || 'Unknown';

    // Create a key that combines country and city for more detailed tracking
    let locationKey = country;
    if (city !== 'Unknown' && city !== country && city !== 'Local') {
      locationKey = `${country}|${city}`;
    }

    if (!distribution[locationKey]) {
      distribution[locationKey] = {
        country: country,
        city: city !== 'Unknown' && city !== country && city !== 'Local' ? city : null,
        count: 0
      };
    }
    distribution[locationKey].count++;
  });

  return Object.entries(distribution)
    .sort(([,a], [,b]) => b.count - a.count)
    .slice(0, limit)
    .map(([key, data]) => ({
      name: data.country,
      city: data.city,
      count: data.count,
      percentage: (data.count / clicks.length * 100).toFixed(1)
    }));
}

function getHourlyDistribution(clicks) {
  const hours = new Array(24).fill(0);
  clicks.forEach(click => {
    const hour = new Date(click.timestamp).getHours();
    hours[hour]++;
  });

  return hours.map((count, hour) => ({
    hour: `${hour}:00`,
    count,
    percentage: (count / clicks.length * 100).toFixed(1)
  }));
}

function getDailyDistribution(clicks) {
  const days = {};
  clicks.forEach(click => {
    const day = new Date(click.timestamp).toISOString().split('T')[0];
    days[day] = (days[day] || 0) + 1;
  });

  return Object.entries(days)
    .sort(([a], [b]) => new Date(a) - new Date(b))
    .map(([date, count]) => ({ date, count }));
}

function getTopUrls(clicks, urls) {
  const urlClicks = {};
  clicks.forEach(click => {
    urlClicks[click.shortCode] = (urlClicks[click.shortCode] || 0) + 1;
  });

  return Object.entries(urlClicks)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10)
    .map(([shortCode, clickCount]) => {
      const url = urls.find(u => u.shortCode === shortCode);
      return {
        shortCode,
        originalUrl: url?.originalUrl || 'Unknown',
        clicks: clickCount,
        createdAt: url?.createdAt
      };
    });
}

module.exports = router;