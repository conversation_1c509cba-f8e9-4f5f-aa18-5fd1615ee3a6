{"t":{"$date":"2025-05-27T17:29:30.199Z"},"s":"I","c":"<PERSON><PERSON>GOSH","id":1000000005,"ctx":"config","msg":"User updated"}
{"t":{"$date":"2025-05-27T17:29:30.206Z"},"s":"I","c":"MONGOSH","id":1000000048,"ctx":"config","msg":"Loading global configuration file","attr":{"filename":"/etc/mongosh.conf","found":false}}
{"t":{"$date":"2025-05-27T17:29:30.207Z"},"s":"I","c":"MONGOSH","id":1000000000,"ctx":"log","msg":"Starting log","attr":{"execPath":"/usr/bin/mongosh","envInfo":{"EDITOR":null,"NODE_OPTIONS":null,"TERM":null},"version":"2.5.0","distributionKind":"compiled","buildArch":"x64","buildPlatform":"linux","buildTarget":"linux-x64","buildTime":"2025-04-08T13:06:58.245Z","gitVersion":"2b03591a8cde3171c86f11d4217352266f1b1a9c","nodeVersion":"v20.19.0","opensslVersion":"3.0.15+quic","sharedOpenssl":false,"runtimeArch":"x64","runtimePlatform":"linux","runtimeGlibcVersion":"2.39","deps":{"nodeDriverVersion":"6.14.2","libmongocryptVersion":"1.13.0","libmongocryptNodeBindingsVersion":"6.3.0","kerberosVersion":"2.1.0"}}}
{"t":{"$date":"2025-05-27T17:29:30.291Z"},"s":"I","c":"DEVTOOLS-CONNECT","id":1000000049,"ctx":"mongosh-connect","msg":"Loaded system CA list","attr":{"caCount":295,"asyncFallbackError":null,"systemCertsError":null,"messages":[]}}
{"t":{"$date":"2025-05-27T17:29:30.307Z"},"s":"I","c":"DEVTOOLS-CONNECT","id":1000000042,"ctx":"mongosh-connect","msg":"Initiating connection attempt","attr":{"uri":"mongodb://127.0.0.1:27017/urlshortener?directConnection=true&serverSelectionTimeoutMS=2000&appName=mongosh+2.5.0","driver":{"name":"nodejs|mongosh","version":"6.14.2|2.5.0"},"devtoolsConnectVersion":"3.4.1","host":"127.0.0.1:27017"}}
{"t":{"$date":"2025-05-27T17:29:30.314Z"},"s":"I","c":"DEVTOOLS-CONNECT","id":1000000035,"ctx":"mongosh-connect","msg":"Server heartbeat succeeded","attr":{"connectionId":"127.0.0.1:27017"}}
{"t":{"$date":"2025-05-27T17:29:30.379Z"},"s":"I","c":"DEVTOOLS-CONNECT","id":1000000037,"ctx":"mongosh-connect","msg":"Connection attempt finished"}
{"t":{"$date":"2025-05-27T17:29:30.382Z"},"s":"I","c":"MONGOSH","id":1000000010,"ctx":"shell-api","msg":"Initialized context","attr":{"method":"setCtx","arguments":{}}}
{"t":{"$date":"2025-05-27T17:29:30.388Z"},"s":"I","c":"MONGOSH-SNIPPETS","id":1000000019,"ctx":"snippets","msg":"Loaded snippets","attr":{"installdir":"/data/db/.mongodb/mongosh/snippets"}}
{"t":{"$date":"2025-05-27T17:29:30.392Z"},"s":"I","c":"MONGOSH","id":1000000003,"ctx":"repl","msg":"Start loading CLI scripts"}
{"t":{"$date":"2025-05-27T17:29:30.395Z"},"s":"I","c":"MONGOSH","id":1000000013,"ctx":"repl","msg":"Evaluating script passed on the command line"}
{"t":{"$date":"2025-05-27T17:29:30.401Z"},"s":"I","c":"MONGOSH","id":1000000007,"ctx":"repl","msg":"Evaluating input","attr":{"input":"db.clicks.find().sort({timestamp: -1}).limit(1)"}}
