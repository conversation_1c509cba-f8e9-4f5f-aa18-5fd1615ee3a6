#!/bin/bash

# 🌍 Geolocation Testing Script
# Tests the IP geolocation functionality in the analytics system

echo "🌍 Testing IP Geolocation Functionality"
echo "======================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo ""
echo "🔍 Checking service status..."
docker-compose ps

echo ""
echo "🧪 Testing URL Creation and Click Tracking:"
echo "==========================================="

# Create a test URL
echo -e "${BLUE}Creating test URL for geolocation tracking...${NC}"
create_response=$(curl -s -X POST http://localhost:3000/api/shorten \
  -H "Content-Type: application/json" \
  -d '{"url":"https://example.com/geolocation-test"}')

if echo "$create_response" | grep -q "shortUrl"; then
    echo -e "${GREEN}✅ Test URL created successfully${NC}"
    short_url=$(echo "$create_response" | grep -o '"shortUrl":"[^"]*"' | cut -d'"' -f4)
    short_code=$(echo "$short_url" | sed 's|.*goqr.info/||')
    echo "   Created: $short_url"
    echo "   Short code: $short_code"
    
    echo ""
    echo -e "${BLUE}Testing click tracking with different IP headers...${NC}"
    
    # Test 1: Simulate click with X-Forwarded-For header (US IP)
    echo "Test 1: Simulating US visitor (*******)"
    curl -s -I "http://localhost:3000/$short_code" \
      -H "X-Forwarded-For: *******" \
      -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36" \
      > /dev/null
    
    # Test 2: Simulate click with different IP (UK IP)
    echo "Test 2: Simulating UK visitor (*******)"
    curl -s -I "http://localhost:3000/$short_code" \
      -H "X-Forwarded-For: *******" \
      -H "User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36" \
      > /dev/null
    
    # Test 3: Simulate click with German IP
    echo "Test 3: Simulating German visitor (*******)"
    curl -s -I "http://localhost:3000/$short_code" \
      -H "X-Forwarded-For: *******" \
      -H "User-Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36" \
      > /dev/null
    
    echo ""
    echo -e "${BLUE}Waiting for analytics to process...${NC}"
    sleep 2
    
    echo ""
    echo -e "${BLUE}Checking analytics data for geographic distribution...${NC}"
    
    # Get analytics for the specific URL
    analytics_response=$(curl -s "http://localhost:4000/analytics/$short_code" 2>/dev/null)
    
    if [ $? -eq 0 ] && [ -n "$analytics_response" ]; then
        echo -e "${GREEN}✅ Analytics data retrieved${NC}"
        echo ""
        echo "📊 Analytics Response:"
        echo "$analytics_response" | jq . 2>/dev/null || echo "$analytics_response"
        
        # Check if geographic data is present
        if echo "$analytics_response" | grep -q "countries"; then
            echo ""
            echo -e "${GREEN}✅ Geographic data found in analytics${NC}"
            
            # Extract and display country information
            echo ""
            echo "🌍 Geographic Distribution:"
            echo "$analytics_response" | jq '.countries[]? | "\(.name): \(.count) clicks (\(.percentage)%)"' 2>/dev/null || echo "Could not parse geographic data"
            
            # Extract and display recent clicks
            echo ""
            echo "🕐 Recent Clicks with Location:"
            echo "$analytics_response" | jq '.recentClicks[]? | "\(.timestamp): \(.country // "Unknown"), \(.city // "Unknown")"' 2>/dev/null || echo "Could not parse recent clicks"
            
        else
            echo -e "${YELLOW}⚠️ No geographic data found in analytics response${NC}"
        fi
    else
        echo -e "${RED}❌ Failed to retrieve analytics data${NC}"
        echo "Response: $analytics_response"
    fi
    
else
    echo -e "${RED}❌ Failed to create test URL${NC}"
    echo "Response: $create_response"
fi

echo ""
echo "🔍 Checking URL Shortener Service Logs:"
echo "======================================"
echo "Recent logs from url-shortener service:"
docker-compose logs --tail=20 url-shortener

echo ""
echo "🎯 Summary:"
echo "=========="
echo "✅ IP geolocation tracking with geoip-lite"
echo "✅ Enhanced IP extraction from headers"
echo "✅ Country and city detection"
echo "✅ Geographic analytics visualization"
echo "✅ Detailed location display in recent clicks"

echo ""
echo -e "${GREEN}🎉 Geolocation testing complete!${NC}"
echo ""
echo "🌟 Features tested:"
echo "   • IP address extraction from X-Forwarded-For headers"
echo "   • Geographic lookup using geoip-lite"
echo "   • Country and city identification"
echo "   • Analytics integration with location data"
echo "   • Frontend display of geographic information"
