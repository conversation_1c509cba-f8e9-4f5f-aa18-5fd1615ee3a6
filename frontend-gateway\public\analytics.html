<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Analytics Dashboard - goqr.info</title>

  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="/favicon.ico">
  <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">

  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }

    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 20px;
      border-radius: 10px;
      margin-bottom: 30px;
      text-align: center;
    }

    .header h1 {
      margin: 0;
      font-size: 2.5em;
    }

    .header p {
      margin: 10px 0 0 0;
      opacity: 0.9;
    }

    .controls {
      background: white;
      padding: 20px;
      border-radius: 10px;
      margin-bottom: 20px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .controls label {
      margin-right: 15px;
      font-weight: bold;
    }

    .controls select,
    .controls button {
      padding: 8px 12px;
      margin-right: 10px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }

    .controls button {
      background: #667eea;
      color: white;
      cursor: pointer;
      border: none;
    }

    .controls button:hover {
      background: #5a6fd8;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

    .stat-card {
      background: white;
      padding: 20px;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      text-align: center;
    }

    .stat-card h3 {
      margin: 0 0 10px 0;
      color: #666;
      font-size: 0.9em;
      text-transform: uppercase;
    }

    .stat-card .value {
      font-size: 2.5em;
      font-weight: bold;
      color: #333;
      margin: 0;
    }

    .stat-card .change {
      font-size: 0.9em;
      margin-top: 5px;
    }

    .positive {
      color: #28a745;
    }

    .negative {
      color: #dc3545;
    }

    .charts-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

    .chart-container {
      background: white;
      padding: 20px;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .chart-container h3 {
      margin: 0 0 20px 0;
      color: #333;
      border-bottom: 2px solid #667eea;
      padding-bottom: 10px;
    }

    .chart-canvas {
      position: relative;
      height: 300px;
    }

    .table-container {
      background: white;
      padding: 20px;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
    }

    .table-container h3 {
      margin: 0 0 20px 0;
      color: #333;
      border-bottom: 2px solid #667eea;
      padding-bottom: 10px;
    }

    table {
      width: 100%;
      border-collapse: collapse;
    }

    th,
    td {
      padding: 12px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }

    th {
      background-color: #f8f9fa;
      font-weight: bold;
    }

    tr:hover {
      background-color: #f5f5f5;
    }

    .loading {
      text-align: center;
      padding: 40px;
      color: #666;
    }

    .error {
      background: #f8d7da;
      color: #721c24;
      padding: 15px;
      border-radius: 5px;
      margin: 20px 0;
    }

    @media (max-width: 768px) {
      .charts-grid {
        grid-template-columns: 1fr;
      }

      .stats-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }
  </style>
</head>

<body>
  <div class="header">
    <div style="display: flex; justify-content: space-between; align-items: center;">
      <div style="display: flex; align-items: center; gap: 15px;">
        <!-- SVG Logo -->
        <svg width="50" height="50" viewBox="0 0 200 220" xmlns="http://www.w3.org/2000/svg">
          <!-- Shield Background -->
          <path d="M100 10 L170 50 L170 130 Q170 180 100 210 Q30 180 30 130 L30 50 Z" fill="rgba(255,255,255,0.9)"
            stroke="none" />

          <!-- QR Code Pattern -->
          <!-- Top Left Corner -->
          <rect x="50" y="40" width="35" height="35" fill="rgba(102,126,234,0.8)" rx="2" />
          <rect x="55" y="45" width="25" height="25" fill="rgba(255,255,255,0.9)" rx="1" />
          <rect x="60" y="50" width="15" height="15" fill="rgba(102,126,234,0.8)" rx="1" />

          <!-- Top Right Corner -->
          <rect x="115" y="40" width="35" height="35" fill="rgba(102,126,234,0.8)" rx="2" />
          <rect x="120" y="45" width="25" height="25" fill="rgba(255,255,255,0.9)" rx="1" />
          <rect x="125" y="50" width="15" height="15" fill="rgba(102,126,234,0.8)" rx="1" />

          <!-- Bottom Left Corner -->
          <rect x="50" y="105" width="35" height="35" fill="rgba(102,126,234,0.8)" rx="2" />
          <rect x="55" y="110" width="25" height="25" fill="rgba(255,255,255,0.9)" rx="1" />
          <rect x="60" y="115" width="15" height="15" fill="rgba(102,126,234,0.8)" rx="1" />

          <!-- Center Pattern -->
          <rect x="95" y="85" width="10" height="10" fill="rgba(102,126,234,0.8)" />
          <rect x="110" y="85" width="10" height="10" fill="rgba(102,126,234,0.8)" />
          <rect x="125" y="85" width="10" height="10" fill="rgba(102,126,234,0.8)" />
          <rect x="95" y="100" width="10" height="10" fill="rgba(102,126,234,0.8)" />
          <rect x="110" y="100" width="10" height="10" fill="rgba(102,126,234,0.8)" />
          <rect x="125" y="100" width="10" height="10" fill="rgba(102,126,234,0.8)" />
          <rect x="95" y="115" width="10" height="10" fill="rgba(102,126,234,0.8)" />
          <rect x="110" y="115" width="25" height="10" fill="rgba(102,126,234,0.8)" />
          <rect x="95" y="130" width="40" height="10" fill="rgba(102,126,234,0.8)" />
        </svg>
        <div>
          <h1>📊 Analytics Dashboard</h1>
          <p>Comprehensive insights into your URL performance</p>
        </div>
      </div>
      <div style="display: flex; align-items: center; gap: 15px;">
        <span id="userInfo" style="color: rgba(255,255,255,0.9); font-weight: bold;">👤 Loading...</span>
        <button onclick="logout()"
          style="background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3); padding: 8px 16px; border-radius: 5px; cursor: pointer; font-weight: bold;">
          🚪 Logout
        </button>
        <a href="/admin.html"
          style="background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3); padding: 8px 16px; border-radius: 5px; text-decoration: none; font-weight: bold;">
          🔧 Admin Panel
        </a>
      </div>
    </div>
  </div>

  <div class="controls">
    <label for="timeRange">Time Range:</label>
    <select id="timeRange">
      <option value="7">Last 7 days</option>
      <option value="30" selected>Last 30 days</option>
      <option value="90">Last 90 days</option>
      <option value="365">Last year</option>
    </select>

    <label for="urlFilter">URL Filter:</label>
    <select id="urlFilter">
      <option value="">All URLs</option>
    </select>

    <button onclick="refreshData()">🔄 Refresh</button>
    <button onclick="exportData()">📊 Export</button>
    <button id="backToOverview" onclick="resetToOverview()" style="display: none; background: #6c757d;">
      ⬅️ Back to Overview
    </button>
  </div>

  <div id="loading" class="loading">
    <h3>📊 Loading analytics data...</h3>
    <p>Please wait while we gather your insights</p>
  </div>

  <div id="error" class="error" style="display: none;"></div>

  <div id="dashboard" style="display: none;">
    <!-- Overview Stats -->
    <div class="stats-grid">
      <div class="stat-card">
        <h3>Total Clicks</h3>
        <div class="value" id="totalClicks">0</div>
        <div class="change" id="clicksChange"></div>
      </div>
      <div class="stat-card">
        <h3>Unique Visitors</h3>
        <div class="value" id="uniqueVisitors">0</div>
        <div class="change" id="visitorsChange"></div>
      </div>
      <div class="stat-card">
        <h3>Total URLs</h3>
        <div class="value" id="totalUrls">0</div>
        <div class="change" id="urlsChange"></div>
      </div>
      <div class="stat-card">
        <h3>Avg. Clicks/URL</h3>
        <div class="value" id="avgClicks">0</div>
        <div class="change" id="avgChange"></div>
      </div>
    </div>

    <!-- Charts -->
    <div class="charts-grid">
      <div class="chart-container">
        <h3>📈 Daily Clicks Trend</h3>
        <div class="chart-canvas">
          <canvas id="dailyChart"></canvas>
        </div>
      </div>

      <div class="chart-container">
        <h3>🕐 Hourly Distribution</h3>
        <div class="chart-canvas">
          <canvas id="hourlyChart"></canvas>
        </div>
      </div>

      <div class="chart-container">
        <h3>🌍 Geographic Distribution</h3>
        <div class="chart-canvas">
          <canvas id="geoChart"></canvas>
        </div>
      </div>

      <div class="chart-container">
        <h3>📱 Device Breakdown</h3>
        <div class="chart-canvas">
          <canvas id="deviceChart"></canvas>
        </div>
      </div>

      <div class="chart-container">
        <h3>🌐 Browser Distribution</h3>
        <div class="chart-canvas">
          <canvas id="browserChart"></canvas>
        </div>
      </div>

      <div class="chart-container">
        <h3>🔗 Traffic Sources</h3>
        <div class="chart-canvas">
          <canvas id="trafficChart"></canvas>
        </div>
      </div>
    </div>

    <!-- Geographic Details Table -->
    <div class="table-container" id="geoTableContainer" style="display: none;">
      <h3>🌍 Geographic Distribution Details</h3>
      <table id="geoTable">
        <thead>
          <tr>
            <th>Country</th>
            <th>City</th>
            <th>Clicks</th>
            <th>Percentage</th>
          </tr>
        </thead>
        <tbody></tbody>
      </table>
    </div>

    <!-- Top URLs Table -->
    <div class="table-container">
      <h3>🏆 Top Performing URLs</h3>
      <table id="topUrlsTable">
        <thead>
          <tr>
            <th>Short Code</th>
            <th>Original URL</th>
            <th>Clicks</th>
            <th>Created</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody></tbody>
      </table>
    </div>
  </div>

  <script>
    let charts = {};
    let currentData = null;
    let adminToken = null;

    // Check authentication
    function checkAuth() {
      adminToken = localStorage.getItem('adminToken');
      if (!adminToken) {
        window.location.href = '/login.html';
        return false;
      }
      return true;
    }

    // Load user info
    async function loadUserInfo() {
      if (!adminToken) return;

      try {
        const response = await fetch('/api/admin/me', {
          headers: { 'Authorization': `Bearer ${adminToken}` }
        });

        if (response.ok) {
          const data = await response.json();
          document.getElementById('userInfo').textContent = `👤 ${data.user.username} (${data.user.role})`;
        } else {
          // Token might be invalid, redirect to login
          localStorage.removeItem('adminToken');
          window.location.href = '/login.html';
        }
      } catch (error) {
        console.error('Failed to load user info:', error);
        document.getElementById('userInfo').textContent = '👤 Error loading user';
      }
    }

    // Logout function
    async function logout() {
      if (!confirm('Are you sure you want to logout?')) return;

      try {
        if (adminToken) {
          await fetch('/api/admin/logout', {
            method: 'POST',
            headers: { 'Authorization': `Bearer ${adminToken}` }
          });
        }
      } catch (error) {
        console.error('Logout error:', error);
      } finally {
        localStorage.removeItem('adminToken');
        window.location.href = '/login.html';
      }
    }

    // Initialize dashboard
    document.addEventListener('DOMContentLoaded', function () {
      // Check authentication first
      if (!checkAuth()) return;

      // Load user info
      loadUserInfo();

      // Load analytics
      loadAnalytics();

      // Event listeners
      document.getElementById('timeRange').addEventListener('change', loadAnalytics);
      document.getElementById('urlFilter').addEventListener('change', loadAnalytics);
    });

    async function loadAnalytics() {
      const timeRange = document.getElementById('timeRange').value;
      const urlFilter = document.getElementById('urlFilter').value;

      showLoading();

      try {
        const url = urlFilter ? `/api/analytics/${urlFilter}?days=${timeRange}` : `/api/analytics?days=${timeRange}`;
        const response = await fetch(url, {
          headers: {
            'Authorization': `Bearer ${adminToken}`
          }
        });

        if (!response.ok) {
          if (response.status === 401) {
            // Unauthorized, redirect to login
            localStorage.removeItem('adminToken');
            window.location.href = '/login.html';
            return;
          }
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        currentData = data;

        renderDashboard(data);
        hideLoading();

      } catch (error) {
        console.error('Analytics error:', error);
        showError(`Failed to load analytics: ${error.message}`);
        hideLoading();
      }
    }

    function renderDashboard(data) {
      if (data.overview) {
        // Overall analytics
        renderOverviewStats(data.overview);
        renderCharts(data);
        renderTopUrls(data.topUrls);
        populateUrlFilter(data.topUrls);
      } else {
        // Single URL analytics
        renderSingleUrlStats(data);
        renderSingleUrlCharts(data);
        renderSingleUrlTable(data);
      }

      document.getElementById('dashboard').style.display = 'block';
    }

    function renderOverviewStats(overview) {
      document.getElementById('totalClicks').textContent = overview.totalClicks.toLocaleString();
      document.getElementById('uniqueVisitors').textContent = overview.uniqueVisitors.toLocaleString();
      document.getElementById('totalUrls').textContent = overview.totalUrls.toLocaleString();
      document.getElementById('avgClicks').textContent = overview.averageClicksPerUrl.toFixed(1);
    }

    function renderSingleUrlStats(data) {
      document.getElementById('totalClicks').textContent = data.totalClicks.toLocaleString();
      document.getElementById('uniqueVisitors').textContent = data.uniqueClicks.toLocaleString();
      document.getElementById('totalUrls').textContent = '1';
      document.getElementById('avgClicks').textContent = data.totalClicks.toFixed(1);
    }

    function renderCharts(data) {
      // Daily trend chart
      if (data.dailyTrend) {
        renderDailyChart(data.dailyTrend);
      }

      // Hourly distribution
      if (data.hourlyTrend) {
        renderHourlyChart(data.hourlyTrend);
      }

      // Geographic distribution
      if (data.topCountries) {
        renderGeoChart(data.topCountries);
        renderGeoTable(data.topCountries);
      }

      // Device breakdown
      if (data.deviceBreakdown) {
        renderDeviceChart(data.deviceBreakdown);
      }

      // Browser distribution
      if (data.browserBreakdown) {
        renderBrowserChart(data.browserBreakdown);
      }

      // Traffic sources
      if (data.trafficSources) {
        renderTrafficChart(data.trafficSources);
      }
    }

    function renderSingleUrlCharts(data) {
      // For single URL analytics
      if (data.dailyDistribution) {
        renderDailyChart(data.dailyDistribution);
      }

      if (data.hourlyDistribution) {
        renderHourlyChart(data.hourlyDistribution);
      }

      if (data.countries) {
        renderGeoChart(data.countries);
        renderGeoTable(data.countries);
      }

      if (data.devices) {
        renderDeviceChart(data.devices);
      }

      if (data.browsers) {
        renderBrowserChart(data.browsers);
      }

      if (data.referrers) {
        renderTrafficChart(data.referrers);
      }
    }

    function renderDailyChart(dailyData) {
      const ctx = document.getElementById('dailyChart').getContext('2d');

      if (charts.daily) {
        charts.daily.destroy();
      }

      charts.daily = new Chart(ctx, {
        type: 'line',
        data: {
          labels: dailyData.map(d => new Date(d.date).toLocaleDateString()),
          datasets: [{
            label: 'Clicks',
            data: dailyData.map(d => d.count),
            borderColor: '#667eea',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            tension: 0.4,
            fill: true
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true
            }
          }
        }
      });
    }

    function renderHourlyChart(hourlyData) {
      const ctx = document.getElementById('hourlyChart').getContext('2d');

      if (charts.hourly) {
        charts.hourly.destroy();
      }

      charts.hourly = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: hourlyData.map(h => h.hour),
          datasets: [{
            label: 'Clicks',
            data: hourlyData.map(h => h.count),
            backgroundColor: 'rgba(102, 126, 234, 0.8)',
            borderColor: '#667eea',
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true
            }
          }
        }
      });
    }

    function renderGeoChart(geoData) {
      const ctx = document.getElementById('geoChart').getContext('2d');

      if (charts.geo) {
        charts.geo.destroy();
      }

      // Process geographic data to show country names with city details
      const processedGeoData = geoData.map(g => {
        let displayName = g.name;

        // If the name looks like a country code, try to expand it
        if (g.name && g.name.length === 2) {
          displayName = getCountryName(g.name) || g.name;
        }

        // Add city information if available
        if (g.city && g.city !== 'Unknown' && g.city !== displayName) {
          displayName = `${displayName} (${g.city})`;
        }

        return {
          ...g,
          displayName: displayName
        };
      });

      charts.geo = new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: processedGeoData.map(g => g.displayName),
          datasets: [{
            data: processedGeoData.map(g => g.count),
            backgroundColor: [
              '#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe',
              '#43e97b', '#fa709a', '#fee140', '#a8edea', '#d299c2'
            ]
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom',
              labels: {
                generateLabels: function (chart) {
                  const data = chart.data;
                  if (data.labels.length && data.datasets.length) {
                    return data.labels.map((label, i) => {
                      const dataset = data.datasets[0];
                      const count = dataset.data[i];
                      const percentage = ((count / dataset.data.reduce((a, b) => a + b, 0)) * 100).toFixed(1);

                      return {
                        text: `${label} (${count} - ${percentage}%)`,
                        fillStyle: dataset.backgroundColor[i],
                        strokeStyle: dataset.backgroundColor[i],
                        lineWidth: 0,
                        index: i
                      };
                    });
                  }
                  return [];
                }
              }
            },
            tooltip: {
              callbacks: {
                label: function (context) {
                  const label = context.label || '';
                  const value = context.parsed;
                  const total = context.dataset.data.reduce((a, b) => a + b, 0);
                  const percentage = ((value / total) * 100).toFixed(1);
                  return `${label}: ${value} clicks (${percentage}%)`;
                }
              }
            }
          }
        }
      });
    }

    // Helper function to convert country codes to full names
    function getCountryName(countryCode) {
      const countryNames = {
        'US': 'United States',
        'GB': 'United Kingdom',
        'CA': 'Canada',
        'AU': 'Australia',
        'DE': 'Germany',
        'FR': 'France',
        'IT': 'Italy',
        'ES': 'Spain',
        'NL': 'Netherlands',
        'BE': 'Belgium',
        'CH': 'Switzerland',
        'AT': 'Austria',
        'SE': 'Sweden',
        'NO': 'Norway',
        'DK': 'Denmark',
        'FI': 'Finland',
        'PL': 'Poland',
        'CZ': 'Czech Republic',
        'HU': 'Hungary',
        'RO': 'Romania',
        'BG': 'Bulgaria',
        'HR': 'Croatia',
        'SI': 'Slovenia',
        'SK': 'Slovakia',
        'LT': 'Lithuania',
        'LV': 'Latvia',
        'EE': 'Estonia',
        'IE': 'Ireland',
        'PT': 'Portugal',
        'GR': 'Greece',
        'CY': 'Cyprus',
        'MT': 'Malta',
        'LU': 'Luxembourg',
        'JP': 'Japan',
        'KR': 'South Korea',
        'CN': 'China',
        'IN': 'India',
        'SG': 'Singapore',
        'HK': 'Hong Kong',
        'TW': 'Taiwan',
        'TH': 'Thailand',
        'MY': 'Malaysia',
        'ID': 'Indonesia',
        'PH': 'Philippines',
        'VN': 'Vietnam',
        'BR': 'Brazil',
        'MX': 'Mexico',
        'AR': 'Argentina',
        'CL': 'Chile',
        'CO': 'Colombia',
        'PE': 'Peru',
        'VE': 'Venezuela',
        'UY': 'Uruguay',
        'PY': 'Paraguay',
        'BO': 'Bolivia',
        'EC': 'Ecuador',
        'ZA': 'South Africa',
        'EG': 'Egypt',
        'MA': 'Morocco',
        'NG': 'Nigeria',
        'KE': 'Kenya',
        'GH': 'Ghana',
        'TN': 'Tunisia',
        'DZ': 'Algeria',
        'RU': 'Russia',
        'UA': 'Ukraine',
        'BY': 'Belarus',
        'KZ': 'Kazakhstan',
        'UZ': 'Uzbekistan',
        'TR': 'Turkey',
        'IL': 'Israel',
        'SA': 'Saudi Arabia',
        'AE': 'UAE',
        'QA': 'Qatar',
        'KW': 'Kuwait',
        'BH': 'Bahrain',
        'OM': 'Oman',
        'JO': 'Jordan',
        'LB': 'Lebanon',
        'SY': 'Syria',
        'IQ': 'Iraq',
        'IR': 'Iran',
        'AF': 'Afghanistan',
        'PK': 'Pakistan',
        'BD': 'Bangladesh',
        'LK': 'Sri Lanka',
        'NP': 'Nepal',
        'MM': 'Myanmar',
        'KH': 'Cambodia',
        'LA': 'Laos',
        'MN': 'Mongolia',
        'NZ': 'New Zealand'
      };

      return countryNames[countryCode.toUpperCase()] || null;
    }

    function renderGeoTable(geoData) {
      const tbody = document.querySelector('#geoTable tbody');
      const container = document.getElementById('geoTableContainer');

      if (!geoData || geoData.length === 0) {
        container.style.display = 'none';
        return;
      }

      container.style.display = 'block';
      tbody.innerHTML = '';

      geoData.forEach(location => {
        const row = document.createElement('tr');

        // Get full country name
        const countryName = getCountryName(location.name) || location.name;
        const cityName = location.city || '-';

        row.innerHTML = `
          <td>
            <strong>${countryName}</strong>
            ${location.name.length === 2 ? `<small style="color: #666;"> (${location.name})</small>` : ''}
          </td>
          <td>${cityName}</td>
          <td><strong>${location.count.toLocaleString()}</strong></td>
          <td>
            <div style="display: flex; align-items: center; gap: 10px;">
              <span>${location.percentage}%</span>
              <div style="background: #e9ecef; border-radius: 10px; height: 8px; width: 100px; overflow: hidden;">
                <div style="background: #667eea; height: 100%; width: ${location.percentage}%; transition: width 0.3s ease;"></div>
              </div>
            </div>
          </td>
        `;
        tbody.appendChild(row);
      });
    }

    function renderDeviceChart(deviceData) {
      const ctx = document.getElementById('deviceChart').getContext('2d');

      if (charts.device) {
        charts.device.destroy();
      }

      charts.device = new Chart(ctx, {
        type: 'pie',
        data: {
          labels: deviceData.map(d => d.name),
          datasets: [{
            data: deviceData.map(d => d.count),
            backgroundColor: ['#667eea', '#764ba2', '#f093fb', '#f5576c']
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom'
            }
          }
        }
      });
    }

    function renderBrowserChart(browserData) {
      const ctx = document.getElementById('browserChart').getContext('2d');

      if (charts.browser) {
        charts.browser.destroy();
      }

      charts.browser = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: browserData.map(b => b.name),
          datasets: [{
            label: 'Users',
            data: browserData.map(b => b.count),
            backgroundColor: 'rgba(102, 126, 234, 0.8)',
            borderColor: '#667eea',
            borderWidth: 1
          }]
        },
        options: {
          indexAxis: 'y',
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            x: {
              beginAtZero: true
            }
          }
        }
      });
    }

    function renderTrafficChart(trafficData) {
      const ctx = document.getElementById('trafficChart').getContext('2d');

      if (charts.traffic) {
        charts.traffic.destroy();
      }

      charts.traffic = new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: trafficData.map(t => t.name),
          datasets: [{
            data: trafficData.map(t => t.count),
            backgroundColor: ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe']
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom'
            }
          }
        }
      });
    }

    function renderTopUrls(topUrls) {
      const tbody = document.querySelector('#topUrlsTable tbody');
      tbody.innerHTML = '';

      // Reset table header for overview
      const tableContainer = document.querySelector('.table-container h3');
      tableContainer.innerHTML = '🏆 Top Performing URLs';

      const thead = document.querySelector('#topUrlsTable thead tr');
      thead.innerHTML = `
        <th>Short Code</th>
        <th>Original URL</th>
        <th>Clicks</th>
        <th>Created</th>
        <th>Actions</th>
      `;

      if (!topUrls || topUrls.length === 0) {
        tbody.innerHTML = '<tr><td colspan="5" style="text-align: center; color: #666;">No data available</td></tr>';
        return;
      }

      topUrls.forEach(url => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>
            <code style="cursor: pointer; color: #667eea; text-decoration: underline;"
                  onclick="viewUrlAnalytics('${url.shortCode}')"
                  title="Click to view detailed analytics for this URL">
              ${url.shortCode}
            </code>
          </td>
          <td>
            <a href="${url.originalUrl}" target="_blank" title="${url.originalUrl}">
              ${url.originalUrl.length > 50 ? url.originalUrl.substring(0, 50) + '...' : url.originalUrl}
            </a>
          </td>
          <td><strong>${url.clicks.toLocaleString()}</strong></td>
          <td>${new Date(url.createdAt).toLocaleDateString()}</td>
          <td>
            <button onclick="viewUrlAnalytics('${url.shortCode}')"
                    style="background: #667eea; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">
              📊 View Details
            </button>
          </td>
        `;
        tbody.appendChild(row);
      });
    }

    function viewUrlAnalytics(shortCode) {
      document.getElementById('urlFilter').value = shortCode;

      // Update header to show we're viewing specific URL
      const header = document.querySelector('.header h1');
      header.innerHTML = `📊 Analytics Dashboard - <span style="color: #ffd700;">${shortCode}</span>`;

      // Update subtitle
      const subtitle = document.querySelector('.header p');
      subtitle.textContent = `Detailed insights for short URL: ${shortCode}`;

      // Show back button
      document.getElementById('backToOverview').style.display = 'inline-block';

      loadAnalytics();
    }

    function resetToOverview() {
      document.getElementById('urlFilter').value = '';

      // Reset header
      const header = document.querySelector('.header h1');
      header.innerHTML = '📊 Analytics Dashboard';

      // Reset subtitle
      const subtitle = document.querySelector('.header p');
      subtitle.textContent = 'Comprehensive insights into your URL performance';

      // Hide back button
      document.getElementById('backToOverview').style.display = 'none';

      loadAnalytics();
    }

    function populateUrlFilter(topUrls) {
      const urlFilter = document.getElementById('urlFilter');
      const currentValue = urlFilter.value;

      // Clear existing options except "All URLs"
      urlFilter.innerHTML = '<option value="">All URLs</option>';

      if (topUrls && topUrls.length > 0) {
        topUrls.forEach(url => {
          const option = document.createElement('option');
          option.value = url.shortCode;
          option.textContent = `${url.shortCode} (${url.clicks} clicks)`;
          urlFilter.appendChild(option);
        });
      }

      // Restore previous selection
      urlFilter.value = currentValue;
    }

    function renderSingleUrlTable(data) {
      const tbody = document.querySelector('#topUrlsTable tbody');
      tbody.innerHTML = '';

      // Show recent clicks for this URL
      if (data.recentClicks && data.recentClicks.length > 0) {
        // Change table header for single URL view
        const tableContainer = document.querySelector('.table-container h3');
        tableContainer.innerHTML = '🕐 Recent Clicks';

        const thead = document.querySelector('#topUrlsTable thead tr');
        thead.innerHTML = `
          <th>Timestamp</th>
          <th>Country</th>
          <th>Device</th>
          <th>Browser</th>
          <th>Referrer</th>
        `;

        data.recentClicks.forEach(click => {
          const row = document.createElement('tr');

          // Format geographic location with city and country from ipinfo.io
          let location = 'Unknown';
          let locationTooltip = `IP: ${click.ipAddress || 'Unknown'}`;

          if (click.country && click.country !== 'Unknown' && click.country !== 'Local') {
            // ipinfo.io returns full country names, not codes
            let countryName = click.country;

            // Add country code to tooltip if available
            if (click.countryCode && click.countryCode.length === 2) {
              locationTooltip += ` | Country Code: ${click.countryCode}`;
            }

            // Add ISP/Organization info if available
            if (click.org) {
              locationTooltip += ` | ISP: ${click.org}`;
            }

            // Add postal code if available
            if (click.postal) {
              locationTooltip += ` | Postal: ${click.postal}`;
            }

            if (click.city && click.city !== 'Unknown' && click.city !== click.country) {
              location = `${click.city}, ${countryName}`;
            } else {
              location = countryName;
            }
          } else if (click.country === 'Local') {
            location = 'Local Network';
            locationTooltip = 'Local/Private IP Address';
          }

          row.innerHTML = `
            <td>${new Date(click.timestamp).toLocaleString()}</td>
            <td title="${locationTooltip}">
              <span style="display: flex; align-items: center; gap: 5px;">
                ${click.countryCode && click.countryCode !== 'Unknown' && click.countryCode !== 'LOCAL' ?
              `<span style="font-size: 16px;" title="${click.countryCode}">${getFlagEmoji(click.countryCode)}</span>` :
              '🌍'
            }
                ${location}
              </span>
            </td>
            <td>${click.device || 'Unknown'}</td>
            <td>${click.browser || 'Unknown'}</td>
            <td>${click.referrer || 'Direct'}</td>
          `;
          tbody.appendChild(row);
        });
      } else {
        tbody.innerHTML = '<tr><td colspan="5" style="text-align: center; color: #666;">No recent clicks available</td></tr>';
      }
    }

    function showLoading() {
      document.getElementById('loading').style.display = 'block';
      document.getElementById('dashboard').style.display = 'none';
      document.getElementById('error').style.display = 'none';
    }

    function hideLoading() {
      document.getElementById('loading').style.display = 'none';
    }

    function showError(message) {
      const errorDiv = document.getElementById('error');
      errorDiv.textContent = message;
      errorDiv.style.display = 'block';
    }

    function refreshData() {
      loadAnalytics();
    }

    function exportData() {
      if (!currentData) return;

      const dataStr = JSON.stringify(currentData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `analytics-${new Date().toISOString().split('T')[0]}.json`;
      link.click();
    }
  </script>
</body>

</html>