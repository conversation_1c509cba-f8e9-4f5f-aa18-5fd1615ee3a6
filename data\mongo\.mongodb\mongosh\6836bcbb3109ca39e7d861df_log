{"t":{"$date":"2025-05-28T07:35:23.698Z"},"s":"I","c":"<PERSON>ONG<PERSON><PERSON>","id":1000000005,"ctx":"config","msg":"User updated"}
{"t":{"$date":"2025-05-28T07:35:23.705Z"},"s":"I","c":"MONGOSH","id":1000000048,"ctx":"config","msg":"Loading global configuration file","attr":{"filename":"/etc/mongosh.conf","found":false}}
{"t":{"$date":"2025-05-28T07:35:23.706Z"},"s":"I","c":"MONGOSH","id":1000000000,"ctx":"log","msg":"Starting log","attr":{"execPath":"/usr/bin/mongosh","envInfo":{"EDITOR":null,"NODE_OPTIONS":null,"TERM":null},"version":"2.5.0","distributionKind":"compiled","buildArch":"x64","buildPlatform":"linux","buildTarget":"linux-x64","buildTime":"2025-04-08T13:06:58.245Z","gitVersion":"2b03591a8cde3171c86f11d4217352266f1b1a9c","nodeVersion":"v20.19.0","opensslVersion":"3.0.15+quic","sharedOpenssl":false,"runtimeArch":"x64","runtimePlatform":"linux","runtimeGlibcVersion":"2.39","deps":{"nodeDriverVersion":"6.14.2","libmongocryptVersion":"1.13.0","libmongocryptNodeBindingsVersion":"6.3.0","kerberosVersion":"2.1.0"}}}
{"t":{"$date":"2025-05-28T07:35:23.810Z"},"s":"I","c":"DEVTOOLS-CONNECT","id":1000000049,"ctx":"mongosh-connect","msg":"Loaded system CA list","attr":{"caCount":295,"asyncFallbackError":null,"systemCertsError":null,"messages":[]}}
{"t":{"$date":"2025-05-28T07:35:23.827Z"},"s":"I","c":"DEVTOOLS-CONNECT","id":1000000042,"ctx":"mongosh-connect","msg":"Initiating connection attempt","attr":{"uri":"mongodb://127.0.0.1:27017/urlshortener?directConnection=true&serverSelectionTimeoutMS=2000&appName=mongosh****.0","driver":{"name":"nodejs|mongosh","version":"6.14.2|2.5.0"},"devtoolsConnectVersion":"3.4.1","host":"127.0.0.1:27017"}}
{"t":{"$date":"2025-05-28T07:35:23.834Z"},"s":"I","c":"DEVTOOLS-CONNECT","id":1000000035,"ctx":"mongosh-connect","msg":"Server heartbeat succeeded","attr":{"connectionId":"127.0.0.1:27017"}}
{"t":{"$date":"2025-05-28T07:35:23.897Z"},"s":"I","c":"DEVTOOLS-CONNECT","id":1000000037,"ctx":"mongosh-connect","msg":"Connection attempt finished"}
{"t":{"$date":"2025-05-28T07:35:23.899Z"},"s":"I","c":"MONGOSH","id":1000000010,"ctx":"shell-api","msg":"Initialized context","attr":{"method":"setCtx","arguments":{}}}
{"t":{"$date":"2025-05-28T07:35:23.901Z"},"s":"I","c":"MONGOSH-SNIPPETS","id":1000000019,"ctx":"snippets","msg":"Loaded snippets","attr":{"installdir":"/data/db/.mongodb/mongosh/snippets"}}
{"t":{"$date":"2025-05-28T07:35:23.905Z"},"s":"I","c":"MONGOSH","id":1000000003,"ctx":"repl","msg":"Start loading CLI scripts"}
{"t":{"$date":"2025-05-28T07:35:23.907Z"},"s":"I","c":"MONGOSH","id":1000000013,"ctx":"repl","msg":"Evaluating script passed on the command line"}
{"t":{"$date":"2025-05-28T07:35:23.908Z"},"s":"I","c":"MONGOSH","id":1000000007,"ctx":"repl","msg":"Evaluating input","attr":{"input":"db.clicks.findOne()"}}
{"t":{"$date":"2025-05-28T07:35:23.909Z"},"s":"I","c":"MONGOSH","id":1000000011,"ctx":"shell-api","msg":"Performed API call","attr":{"method":"findOne","class":"Collection","db":"urlshortener","coll":"clicks","arguments":{"query":{},"options":{}}}}
{"t":{"$date":"2025-05-28T07:35:23.913Z"},"s":"I","c":"MONGOSH","id":1000000004,"ctx":"connect","msg":"Connecting to server","attr":{"userId":null,"telemetryAnonymousId":"6835e79f2f1d93f41ed861de","connectionUri":"mongodb://<ip address>:27017/urlshortener?directConnection=true&serverSelectionTimeoutMS=2000&appName=mongosh****.0","mongosh_version":"2.5.0","session_id":"6836bcbb3109ca39e7d861df","is_localhost":true,"is_do_url":false,"is_atlas_url":false,"is_atlas":false,"server_version":"8.0.9","node_version":"v20.19.0","server_os":"linux","server_arch":"x86_64","is_enterprise":false,"auth_type":null,"is_data_federation":false,"is_stream":false,"dl_version":null,"atlas_version":null,"is_genuine":true,"non_genuine_server_name":"mongodb","is_local_atlas":false,"fcv":"8.0","api_version":null,"api_strict":null,"api_deprecation_errors":null,"atlas_hostname":null}}
{"t":{"$date":"2025-05-28T07:35:23.917Z"},"s":"I","c":"MONGOSH","id":1000000045,"ctx":"analytics","msg":"Flushed outstanding data","attr":{"flushError":"Trying to persist throttle state before userId is set","flushDuration":0}}
