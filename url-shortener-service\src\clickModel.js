const mongoose = require('mongoose');

const ClickSchema = new mongoose.Schema({
  shortCode: { type: String, required: true, index: true },
  timestamp: { type: Date, default: Date.now, index: true },

  // Network Information
  ipAddress: String,
  country: String,
  countryCode: String,
  region: String,
  city: String,
  timezone: String,

  // Device & Browser Information
  userAgent: String,
  browser: String,
  browserVersion: String,
  os: String,
  osVersion: String,
  device: String,
  deviceType: String, // mobile, tablet, desktop

  // Referrer Information
  referrer: String,
  referrerDomain: String,
  referrerType: String, // direct, search, social, email, etc.

  // Session Information
  sessionId: String,
  isUnique: { type: Boolean, default: true }, // First visit from this IP/session

  // Performance Metrics
  responseTime: Number, // Time to redirect (ms)

  // Additional Analytics
  language: String,
  screenResolution: String,
  colorDepth: Number,

  // Geolocation (if available)
  latitude: Number,
  longitude: Number,

  // Campaign Tracking
  utmSource: String,
  utmMedium: String,
  utmCampaign: String,
  utmTerm: String,
  utmContent: String
}, { strict: false });

// Indexes for better query performance
ClickSchema.index({ shortCode: 1, timestamp: -1 });
ClickSchema.index({ timestamp: -1 });
ClickSchema.index({ country: 1 });
ClickSchema.index({ deviceType: 1 });
ClickSchema.index({ browser: 1 });
ClickSchema.index({ referrerType: 1 });

module.exports = mongoose.model('Click', ClickSchema);
