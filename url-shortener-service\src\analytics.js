const UAParser = require('ua-parser-js');
const geoip = require('geoip-lite');
const crypto = require('crypto');

/**
 * Analytics utility functions for comprehensive click tracking
 */

// Parse User Agent for device/browser information
function parseUserAgent(userAgent) {
  const parser = new UAParser(userAgent);
  const result = parser.getResult();

  return {
    browser: result.browser.name || 'Unknown',
    browserVersion: result.browser.version || 'Unknown',
    os: result.os.name || 'Unknown',
    osVersion: result.os.version || 'Unknown',
    device: result.device.model || 'Unknown',
    deviceType: getDeviceType(result.device.type) || 'desktop'
  };
}

// Determine device type
function getDeviceType(deviceType) {
  if (!deviceType) return 'desktop';

  switch (deviceType.toLowerCase()) {
    case 'mobile':
      return 'mobile';
    case 'tablet':
      return 'tablet';
    case 'smarttv':
      return 'tv';
    case 'wearable':
      return 'wearable';
    case 'console':
      return 'console';
    default:
      return 'desktop';
  }
}

// Get geographic information from IP using ipinfo.io
async function getGeoInfoFromAPI(ip) {
  // Skip local/private IPs
  if (!ip || ip === '127.0.0.1' || ip === '::1' || ip.startsWith('192.168.') || ip.startsWith('10.') || ip.startsWith('172.')) {
    return {
      country: 'Local',
      countryCode: 'LOCAL',
      region: 'Local',
      city: 'Local',
      timezone: 'Local',
      latitude: null,
      longitude: null,
      org: null,
      postal: null
    };
  }

  // Clean up IP address (remove port if present)
  const cleanIp = ip.split(':')[0];

  console.log(`🌍 Looking up geolocation for IP: ${cleanIp} using ipinfo.io`);

  try {
    // Use ipinfo.io free service (up to 50,000 requests per month)
    const response = await axios.get(`https://ipinfo.io/${cleanIp}/json`, {
      timeout: 5000, // 5 second timeout
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'GoQR.info/1.0'
      }
    });

    const data = response.data;

    // Check if we got valid data
    if (!data || data.error || data.bogon) {
      console.log(`❌ ipinfo.io failed for IP ${cleanIp}: ${data?.error || 'Invalid IP or bogon'}`);
      return getFallbackGeoInfo(cleanIp);
    }

    // Parse location data from ipinfo.io format
    const [latitude, longitude] = data.loc ? data.loc.split(',').map(Number) : [null, null];

    // Get full country name from country code
    const countryName = getCountryNameFromCode(data.country) || data.country || 'Unknown';

    console.log(`✅ ipinfo.io geolocation found for ${cleanIp}: ${data.city}, ${data.region}, ${countryName} (${data.country})`);

    return {
      country: countryName,
      countryCode: data.country || 'Unknown',
      region: data.region || 'Unknown',
      city: data.city || 'Unknown',
      timezone: data.timezone || 'Unknown',
      latitude: latitude || null,
      longitude: longitude || null,
      org: data.org || null, // ISP/Organization info
      postal: data.postal || null // Postal code
    };

  } catch (error) {
    console.log(`❌ Error calling ipinfo.io for IP ${cleanIp}: ${error.message}`);
    return getFallbackGeoInfo(cleanIp);
  }
}

// Fallback to geoip-lite if ipinfo.io fails
function getFallbackGeoInfo(ip) {
  console.log(`🔄 Using fallback geoip-lite for IP: ${ip}`);

  const geo = geoip.lookup(ip);

  if (!geo) {
    console.log(`❌ No geolocation data found for IP: ${ip}`);
    return {
      country: 'Unknown',
      countryCode: 'Unknown',
      region: 'Unknown',
      city: 'Unknown',
      timezone: 'Unknown',
      latitude: null,
      longitude: null,
      org: null,
      postal: null
    };
  }

  console.log(`✅ Fallback geolocation found for ${ip}: ${geo.city}, ${geo.region}, ${geo.country}`);

  return {
    country: getCountryNameFromCode(geo.country) || geo.country || 'Unknown',
    countryCode: geo.country || 'Unknown',
    region: geo.region || 'Unknown',
    city: geo.city || 'Unknown',
    timezone: geo.timezone || 'Unknown',
    latitude: geo.ll ? geo.ll[0] : null,
    longitude: geo.ll ? geo.ll[1] : null,
    org: null,
    postal: null
  };
}

// Helper function to convert country codes to full names
function getCountryNameFromCode(countryCode) {
  const countryNames = {
    'US': 'United States', 'GB': 'United Kingdom', 'CA': 'Canada', 'AU': 'Australia',
    'DE': 'Germany', 'FR': 'France', 'IT': 'Italy', 'ES': 'Spain', 'NL': 'Netherlands',
    'BE': 'Belgium', 'CH': 'Switzerland', 'AT': 'Austria', 'SE': 'Sweden', 'NO': 'Norway',
    'DK': 'Denmark', 'FI': 'Finland', 'PL': 'Poland', 'CZ': 'Czech Republic',
    'HU': 'Hungary', 'RO': 'Romania', 'BG': 'Bulgaria', 'HR': 'Croatia', 'SI': 'Slovenia',
    'SK': 'Slovakia', 'LT': 'Lithuania', 'LV': 'Latvia', 'EE': 'Estonia', 'IE': 'Ireland',
    'PT': 'Portugal', 'GR': 'Greece', 'CY': 'Cyprus', 'MT': 'Malta', 'LU': 'Luxembourg',
    'JP': 'Japan', 'KR': 'South Korea', 'CN': 'China', 'IN': 'India', 'SG': 'Singapore',
    'HK': 'Hong Kong', 'TW': 'Taiwan', 'TH': 'Thailand', 'MY': 'Malaysia', 'ID': 'Indonesia',
    'PH': 'Philippines', 'VN': 'Vietnam', 'BR': 'Brazil', 'MX': 'Mexico', 'AR': 'Argentina',
    'CL': 'Chile', 'CO': 'Colombia', 'PE': 'Peru', 'VE': 'Venezuela', 'UY': 'Uruguay',
    'PY': 'Paraguay', 'BO': 'Bolivia', 'EC': 'Ecuador', 'ZA': 'South Africa', 'EG': 'Egypt',
    'MA': 'Morocco', 'NG': 'Nigeria', 'KE': 'Kenya', 'GH': 'Ghana', 'TN': 'Tunisia',
    'DZ': 'Algeria', 'RU': 'Russia', 'UA': 'Ukraine', 'BY': 'Belarus', 'KZ': 'Kazakhstan',
    'UZ': 'Uzbekistan', 'TR': 'Turkey', 'IL': 'Israel', 'SA': 'Saudi Arabia', 'AE': 'UAE',
    'QA': 'Qatar', 'KW': 'Kuwait', 'BH': 'Bahrain', 'OM': 'Oman', 'JO': 'Jordan',
    'LB': 'Lebanon', 'SY': 'Syria', 'IQ': 'Iraq', 'IR': 'Iran', 'AF': 'Afghanistan',
    'PK': 'Pakistan', 'BD': 'Bangladesh', 'LK': 'Sri Lanka', 'NP': 'Nepal', 'MM': 'Myanmar',
    'KH': 'Cambodia', 'LA': 'Laos', 'MN': 'Mongolia', 'NZ': 'New Zealand'
  };

  return countryNames[countryCode?.toUpperCase()] || null;
}

// Synchronous version for backward compatibility (uses fallback only)
function getGeoInfo(ip) {
  return getFallbackGeoInfo(ip);
}

// Parse referrer information
function parseReferrer(referrer) {
  if (!referrer) {
    return {
      referrer: 'Direct',
      referrerDomain: 'Direct',
      referrerType: 'direct'
    };
  }

  try {
    const url = new URL(referrer);
    const domain = url.hostname.replace(/^www\./, '').toLowerCase();

    return {
      referrer: referrer,
      referrerDomain: domain,
      referrerType: classifyReferrer(domain)
    };
  } catch (error) {
    return {
      referrer: referrer,
      referrerDomain: 'Unknown',
      referrerType: 'unknown'
    };
  }
}

// Classify referrer type
function classifyReferrer(domain) {
  const searchEngines = ['google.com', 'bing.com', 'yahoo.com', 'duckduckgo.com', 'baidu.com'];
  const socialMedia = ['facebook.com', 'twitter.com', 'linkedin.com', 'instagram.com', 'tiktok.com', 'youtube.com'];
  const email = ['gmail.com', 'outlook.com', 'yahoo.com', 'mail.google.com'];

  if (searchEngines.some(engine => domain.includes(engine))) {
    return 'search';
  }

  if (socialMedia.some(social => domain.includes(social))) {
    return 'social';
  }

  if (email.some(mail => domain.includes(mail))) {
    return 'email';
  }

  return 'referral';
}

// Parse UTM parameters from URL
function parseUTMParameters(url) {
  if (!url) return {};

  try {
    const urlObj = new URL(url);
    const params = urlObj.searchParams;

    return {
      utmSource: params.get('utm_source') || null,
      utmMedium: params.get('utm_medium') || null,
      utmCampaign: params.get('utm_campaign') || null,
      utmTerm: params.get('utm_term') || null,
      utmContent: params.get('utm_content') || null
    };
  } catch (error) {
    return {};
  }
}

// Generate session ID
function generateSessionId(ip, userAgent) {
  const data = `${ip}-${userAgent}-${Date.now()}`;
  return crypto.createHash('md5').update(data).digest('hex').substring(0, 16);
}

// Check if this is a unique visit (simplified - in production, use Redis or similar)
function isUniqueVisit(ip, shortCode, timeWindow = 24 * 60 * 60 * 1000) {
  // This is a simplified implementation
  // In production, you'd want to use Redis or a more sophisticated caching mechanism
  return true; // For now, assume all visits are unique
}

// Get language from Accept-Language header
function parseLanguage(acceptLanguage) {
  if (!acceptLanguage) return 'Unknown';

  // Extract primary language
  const languages = acceptLanguage.split(',');
  const primaryLang = languages[0].split(';')[0].trim();

  return primaryLang || 'Unknown';
}

// Comprehensive analytics data collection (async version with ipinfo.io)
async function collectAnalyticsDataAsync(req, shortCode, startTime) {
  const userAgent = req.get('User-Agent') || '';

  // Enhanced IP extraction - handle multiple forwarded IPs
  let ip = req.get('X-Forwarded-For') || req.get('X-Real-IP') || req.ip || req.connection.remoteAddress || '';

  // If X-Forwarded-For contains multiple IPs, take the first one (original client IP)
  if (ip.includes(',')) {
    ip = ip.split(',')[0].trim();
  }

  // Remove IPv6 prefix if present
  if (ip.startsWith('::ffff:')) {
    ip = ip.substring(7);
  }

  console.log(`📍 Extracted IP address: ${ip} for short code: ${shortCode}`);

  const referrer = req.get('Referrer') || req.get('Referer') || '';
  const acceptLanguage = req.get('Accept-Language') || '';

  // Parse all the data
  const deviceInfo = parseUserAgent(userAgent);
  const geoInfo = await getGeoInfoFromAPI(ip); // Use async ipinfo.io lookup
  const referrerInfo = parseReferrer(referrer);
  const utmParams = parseUTMParameters(referrer);
  const sessionId = generateSessionId(ip, userAgent);
  const responseTime = startTime ? Date.now() - startTime : null;
  const language = parseLanguage(acceptLanguage);

  return {
    shortCode,
    timestamp: new Date(),

    // Network Information
    ipAddress: ip,
    ...geoInfo,

    // Device & Browser Information
    userAgent,
    ...deviceInfo,

    // Referrer Information
    ...referrerInfo,

    // Session Information
    sessionId,
    isUnique: isUniqueVisit(ip, shortCode),

    // Performance Metrics
    responseTime,

    // Additional Analytics
    language,

    // Campaign Tracking
    ...utmParams
  };
}

// Synchronous version for backward compatibility
function collectAnalyticsData(req, shortCode, startTime) {
  const userAgent = req.get('User-Agent') || '';

  // Enhanced IP extraction - handle multiple forwarded IPs
  let ip = req.get('X-Forwarded-For') || req.get('X-Real-IP') || req.ip || req.connection.remoteAddress || '';

  // If X-Forwarded-For contains multiple IPs, take the first one (original client IP)
  if (ip.includes(',')) {
    ip = ip.split(',')[0].trim();
  }

  // Remove IPv6 prefix if present
  if (ip.startsWith('::ffff:')) {
    ip = ip.substring(7);
  }

  console.log(`📍 Extracted IP address: ${ip} for short code: ${shortCode}`);

  const referrer = req.get('Referrer') || req.get('Referer') || '';
  const acceptLanguage = req.get('Accept-Language') || '';

  // Parse all the data
  const deviceInfo = parseUserAgent(userAgent);
  const geoInfo = getGeoInfo(ip); // Use synchronous fallback
  const referrerInfo = parseReferrer(referrer);
  const utmParams = parseUTMParameters(referrer);
  const sessionId = generateSessionId(ip, userAgent);
  const responseTime = startTime ? Date.now() - startTime : null;
  const language = parseLanguage(acceptLanguage);

  return {
    shortCode,
    timestamp: new Date(),

    // Network Information
    ipAddress: ip,
    ...geoInfo,

    // Device & Browser Information
    userAgent,
    ...deviceInfo,

    // Referrer Information
    ...referrerInfo,

    // Session Information
    sessionId,
    isUnique: isUniqueVisit(ip, shortCode),

    // Performance Metrics
    responseTime,

    // Additional Analytics
    language,

    // Campaign Tracking
    ...utmParams
  };
}

module.exports = {
  parseUserAgent,
  getGeoInfo,
  getGeoInfoFromAPI,
  parseReferrer,
  parseUTMParameters,
  generateSessionId,
  isUniqueVisit,
  parseLanguage,
  collectAnalyticsData,
  collectAnalyticsDataAsync
};
