const UAParser = require('ua-parser-js');
const geoip = require('geoip-lite');
const crypto = require('crypto');

/**
 * Analytics utility functions for comprehensive click tracking
 */

// Parse User Agent for device/browser information
function parseUserAgent(userAgent) {
  const parser = new UAParser(userAgent);
  const result = parser.getResult();

  return {
    browser: result.browser.name || 'Unknown',
    browserVersion: result.browser.version || 'Unknown',
    os: result.os.name || 'Unknown',
    osVersion: result.os.version || 'Unknown',
    device: result.device.model || 'Unknown',
    deviceType: getDeviceType(result.device.type) || 'desktop'
  };
}

// Determine device type
function getDeviceType(deviceType) {
  if (!deviceType) return 'desktop';

  switch (deviceType.toLowerCase()) {
    case 'mobile':
      return 'mobile';
    case 'tablet':
      return 'tablet';
    case 'smarttv':
      return 'tv';
    case 'wearable':
      return 'wearable';
    case 'console':
      return 'console';
    default:
      return 'desktop';
  }
}

// Get geographic information from IP
function getGeoInfo(ip) {
  // Skip local/private IPs
  if (!ip || ip === '127.0.0.1' || ip === '::1' || ip.startsWith('192.168.') || ip.startsWith('10.') || ip.startsWith('172.')) {
    return {
      country: 'Local',
      region: 'Local',
      city: 'Local',
      timezone: 'Local',
      latitude: null,
      longitude: null
    };
  }

  // Clean up IP address (remove port if present)
  const cleanIp = ip.split(':')[0];

  console.log(`🌍 Looking up geolocation for IP: ${cleanIp}`);

  const geo = geoip.lookup(cleanIp);

  if (!geo) {
    console.log(`❌ No geolocation data found for IP: ${cleanIp}`);
    return {
      country: 'Unknown',
      region: 'Unknown',
      city: 'Unknown',
      timezone: 'Unknown',
      latitude: null,
      longitude: null
    };
  }

  console.log(`✅ Geolocation found for ${cleanIp}: ${geo.city}, ${geo.region}, ${geo.country}`);

  return {
    country: geo.country || 'Unknown',
    region: geo.region || 'Unknown',
    city: geo.city || 'Unknown',
    timezone: geo.timezone || 'Unknown',
    latitude: geo.ll ? geo.ll[0] : null,
    longitude: geo.ll ? geo.ll[1] : null
  };
}

// Parse referrer information
function parseReferrer(referrer) {
  if (!referrer) {
    return {
      referrer: 'Direct',
      referrerDomain: 'Direct',
      referrerType: 'direct'
    };
  }

  try {
    const url = new URL(referrer);
    const domain = url.hostname.replace(/^www\./, '').toLowerCase();

    return {
      referrer: referrer,
      referrerDomain: domain,
      referrerType: classifyReferrer(domain)
    };
  } catch (error) {
    return {
      referrer: referrer,
      referrerDomain: 'Unknown',
      referrerType: 'unknown'
    };
  }
}

// Classify referrer type
function classifyReferrer(domain) {
  const searchEngines = ['google.com', 'bing.com', 'yahoo.com', 'duckduckgo.com', 'baidu.com'];
  const socialMedia = ['facebook.com', 'twitter.com', 'linkedin.com', 'instagram.com', 'tiktok.com', 'youtube.com'];
  const email = ['gmail.com', 'outlook.com', 'yahoo.com', 'mail.google.com'];

  if (searchEngines.some(engine => domain.includes(engine))) {
    return 'search';
  }

  if (socialMedia.some(social => domain.includes(social))) {
    return 'social';
  }

  if (email.some(mail => domain.includes(mail))) {
    return 'email';
  }

  return 'referral';
}

// Parse UTM parameters from URL
function parseUTMParameters(url) {
  if (!url) return {};

  try {
    const urlObj = new URL(url);
    const params = urlObj.searchParams;

    return {
      utmSource: params.get('utm_source') || null,
      utmMedium: params.get('utm_medium') || null,
      utmCampaign: params.get('utm_campaign') || null,
      utmTerm: params.get('utm_term') || null,
      utmContent: params.get('utm_content') || null
    };
  } catch (error) {
    return {};
  }
}

// Generate session ID
function generateSessionId(ip, userAgent) {
  const data = `${ip}-${userAgent}-${Date.now()}`;
  return crypto.createHash('md5').update(data).digest('hex').substring(0, 16);
}

// Check if this is a unique visit (simplified - in production, use Redis or similar)
function isUniqueVisit(ip, shortCode, timeWindow = 24 * 60 * 60 * 1000) {
  // This is a simplified implementation
  // In production, you'd want to use Redis or a more sophisticated caching mechanism
  return true; // For now, assume all visits are unique
}

// Get language from Accept-Language header
function parseLanguage(acceptLanguage) {
  if (!acceptLanguage) return 'Unknown';

  // Extract primary language
  const languages = acceptLanguage.split(',');
  const primaryLang = languages[0].split(';')[0].trim();

  return primaryLang || 'Unknown';
}

// Comprehensive analytics data collection
function collectAnalyticsData(req, shortCode, startTime) {
  const userAgent = req.get('User-Agent') || '';

  // Enhanced IP extraction - handle multiple forwarded IPs
  let ip = req.get('X-Forwarded-For') || req.get('X-Real-IP') || req.ip || req.connection.remoteAddress || '';

  // If X-Forwarded-For contains multiple IPs, take the first one (original client IP)
  if (ip.includes(',')) {
    ip = ip.split(',')[0].trim();
  }

  // Remove IPv6 prefix if present
  if (ip.startsWith('::ffff:')) {
    ip = ip.substring(7);
  }

  console.log(`📍 Extracted IP address: ${ip} for short code: ${shortCode}`);

  const referrer = req.get('Referrer') || req.get('Referer') || '';
  const acceptLanguage = req.get('Accept-Language') || '';

  // Parse all the data
  const deviceInfo = parseUserAgent(userAgent);
  const geoInfo = getGeoInfo(ip);
  const referrerInfo = parseReferrer(referrer);
  const utmParams = parseUTMParameters(referrer);
  const sessionId = generateSessionId(ip, userAgent);
  const responseTime = startTime ? Date.now() - startTime : null;
  const language = parseLanguage(acceptLanguage);

  return {
    shortCode,
    timestamp: new Date(),

    // Network Information
    ipAddress: ip,
    ...geoInfo,

    // Device & Browser Information
    userAgent,
    ...deviceInfo,

    // Referrer Information
    ...referrerInfo,

    // Session Information
    sessionId,
    isUnique: isUniqueVisit(ip, shortCode),

    // Performance Metrics
    responseTime,

    // Additional Analytics
    language,

    // Campaign Tracking
    ...utmParams
  };
}

module.exports = {
  parseUserAgent,
  getGeoInfo,
  parseReferrer,
  parseUTMParameters,
  generateSessionId,
  isUniqueVisit,
  parseLanguage,
  collectAnalyticsData
};
