#!/usr/bin/env node

/**
 * 🌍 IPinfo.io Geolocation Test Script
 * Tests the new ipinfo.io integration for enhanced geolocation
 */

const axios = require('axios');

// Test IP addresses from different countries
const testIPs = [
  { ip: '*******', description: 'Google DNS (US)' },
  { ip: '*******', description: 'Cloudflare DNS' },
  { ip: '**************', description: 'OpenDNS (US)' },
  { ip: '*********', description: 'Yandex DNS (Russia)' },
  { ip: '*******', description: 'Google Secondary DNS' },
  { ip: '*******', description: 'Quad9 DNS' }
];

async function testIPInfo(ip, description) {
  try {
    console.log(`\n🔍 Testing ${description} (${ip}):`);
    console.log('─'.repeat(50));
    
    const response = await axios.get(`https://ipinfo.io/${ip}/json`, {
      timeout: 5000,
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'GoQR.info/1.0'
      }
    });

    const data = response.data;

    if (data.error || data.bogon) {
      console.log(`❌ Failed: ${data.error || 'Bogon IP (private/reserved)'}`);
      return { success: false, error: data.error || 'Bogon IP' };
    }

    console.log(`✅ Success!`);
    console.log(`   🌍 Country: ${data.country || 'Unknown'}`);
    console.log(`   🏙️  City: ${data.city || 'Unknown'}`);
    console.log(`   📍 Region: ${data.region || 'Unknown'}`);
    console.log(`   🕐 Timezone: ${data.timezone || 'Unknown'}`);
    console.log(`   📊 Location: ${data.loc || 'N/A'}`);
    console.log(`   🏢 Organization: ${data.org || 'Unknown'}`);
    console.log(`   📮 Postal: ${data.postal || 'Unknown'}`);
    
    // Test flag emoji generation
    if (data.country) {
      const flagEmoji = getFlagEmoji(data.country);
      console.log(`   🏳️  Flag: ${flagEmoji}`);
    }
    
    return {
      success: true,
      country: data.country,
      city: data.city,
      region: data.region,
      timezone: data.timezone,
      org: data.org,
      postal: data.postal
    };
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    return { success: false, error: error.message };
  }
}

function getFlagEmoji(countryCode) {
  if (!countryCode || countryCode.length !== 2) return '🌍';
  
  const codePoints = countryCode
    .toUpperCase()
    .split('')
    .map(char => 127397 + char.charCodeAt());
  
  return String.fromCodePoint(...codePoints);
}

async function testGoQRIntegration() {
  console.log('\n🧪 Testing GoQR.info Integration with ipinfo.io:');
  console.log('═'.repeat(50));
  
  try {
    // Test creating a URL
    console.log('📝 Creating test URL...');
    const createResponse = await axios.post('http://localhost:3000/api/shorten', {
      url: 'https://example.com/ipinfo-test'
    }, {
      headers: { 'Content-Type': 'application/json' },
      timeout: 10000
    });

    if (createResponse.data.shortUrl) {
      const shortCode = createResponse.data.shortUrl.split('/').pop();
      console.log(`✅ Test URL created: ${createResponse.data.shortUrl}`);
      console.log(`   Short code: ${shortCode}`);
      
      // Test clicking the URL with different IP headers
      console.log('\n🖱️  Testing clicks with different IP headers...');
      
      const testClicks = [
        { ip: '*******', description: 'US visitor (Google DNS)' },
        { ip: '*********', description: 'Russian visitor (Yandex DNS)' },
        { ip: '*******', description: 'Quad9 DNS visitor' }
      ];
      
      for (const testClick of testClicks) {
        console.log(`\n   Testing ${testClick.description} (${testClick.ip}):`);
        try {
          await axios.get(`http://localhost:3000/${shortCode}`, {
            headers: {
              'X-Forwarded-For': testClick.ip,
              'User-Agent': 'Mozilla/5.0 (Test Browser) ipinfo-test'
            },
            maxRedirects: 0,
            validateStatus: (status) => status === 302 || status === 301
          });
          console.log(`   ✅ Click tracked successfully`);
        } catch (error) {
          if (error.response && (error.response.status === 301 || error.response.status === 302)) {
            console.log(`   ✅ Click tracked successfully (redirected)`);
          } else {
            console.log(`   ❌ Click tracking failed: ${error.message}`);
          }
        }
      }
      
      // Wait for analytics to process
      console.log('\n⏳ Waiting for analytics to process...');
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Check analytics
      console.log('\n📊 Checking analytics data...');
      try {
        const analyticsResponse = await axios.get(`http://localhost:4000/analytics/${shortCode}`);
        const analytics = analyticsResponse.data;
        
        console.log(`✅ Analytics retrieved successfully`);
        console.log(`   Total clicks: ${analytics.totalClicks || 0}`);
        
        if (analytics.recentClicks && analytics.recentClicks.length > 0) {
          console.log(`   Recent clicks with ipinfo.io geolocation:`);
          analytics.recentClicks.forEach((click, index) => {
            const location = click.city && click.city !== 'Unknown' ? 
              `${click.city}, ${click.country}` : 
              click.country || 'Unknown';
            const org = click.org ? ` | ${click.org}` : '';
            const postal = click.postal ? ` | Postal: ${click.postal}` : '';
            console.log(`     ${index + 1}. ${location} (${click.countryCode || 'N/A'}) - IP: ${click.ipAddress || 'Unknown'}${org}${postal}`);
          });
        } else {
          console.log(`   ⚠️  No recent clicks found in analytics`);
        }
        
        if (analytics.countries && analytics.countries.length > 0) {
          console.log(`   Geographic distribution:`);
          analytics.countries.forEach((country, index) => {
            console.log(`     ${index + 1}. ${country.name} - ${country.count} clicks (${country.percentage}%)`);
          });
        }
        
      } catch (error) {
        console.log(`❌ Failed to retrieve analytics: ${error.message}`);
      }
      
    } else {
      console.log('❌ Failed to create test URL');
    }
  } catch (error) {
    console.log(`❌ Integration test failed: ${error.message}`);
  }
}

async function main() {
  console.log('🌍 IPinfo.io Geolocation Testing');
  console.log('═'.repeat(50));
  console.log('Testing enhanced geolocation with ipinfo.io service');
  console.log('This replaces the previous ip-api.com implementation');
  
  // Test ipinfo.io directly
  console.log('\n📡 Testing ipinfo.io API directly:');
  const results = [];
  
  for (const test of testIPs) {
    const result = await testIPInfo(test.ip, test.description);
    results.push({ ...test, ...result });
    
    // Rate limiting - ipinfo.io allows 50,000 requests per month for free
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // Summary
  console.log('\n📋 Summary:');
  console.log('═'.repeat(50));
  const successful = results.filter(r => r.success).length;
  console.log(`✅ Successful lookups: ${successful}/${results.length}`);
  console.log(`❌ Failed lookups: ${results.length - successful}/${results.length}`);
  
  if (successful > 0) {
    console.log('\n🌍 Countries detected:');
    const countries = [...new Set(results.filter(r => r.success).map(r => r.country))];
    countries.forEach(country => console.log(`   • ${country}`));
    
    console.log('\n🏙️ Cities detected:');
    const cities = [...new Set(results.filter(r => r.success && r.city).map(r => `${r.city}, ${r.country}`))];
    cities.forEach(city => console.log(`   • ${city}`));
  }
  
  // Test integration with GoQR.info
  await testGoQRIntegration();
  
  console.log('\n🎉 Testing complete!');
  console.log('\n💡 Benefits of ipinfo.io integration:');
  console.log('   • More accurate geolocation data');
  console.log('   • Full country names (not just codes)');
  console.log('   • Detailed city and region information');
  console.log('   • ISP/Organization details');
  console.log('   • Postal code information');
  console.log('   • Enhanced analytics display');
  console.log('   • Country flag emojis');
  console.log('   • Fallback to geoip-lite if API fails');
  console.log('   • 50,000 free requests per month');
}

// Run the tests
main().catch(console.error);
