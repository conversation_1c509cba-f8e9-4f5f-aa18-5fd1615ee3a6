const express = require('express');
const connectDB = require('./db');
const routes = require('./routes');
const cors = require('cors');

const app = express();

// Trust proxy to properly handle X-Forwarded-For headers
app.set('trust proxy', true);

app.use(cors());
app.use(express.json());

connectDB();
app.use('/', routes);

const PORT = 4000;
app.listen(PORT, () => console.log(`URL Shortener Service running on port ${PORT}`));
